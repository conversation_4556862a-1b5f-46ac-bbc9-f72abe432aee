'use client';

import { useState } from 'react';
import { Heart, Sparkles, MessageCircle, Users, Zap } from 'lucide-react';
import { motion } from 'framer-motion';
import Preloader from '@/components/Preloader';
import Navigation from '@/components/Navigation';
import AppStoreBadges from '@/components/AppStoreBadges';

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  const handlePreloaderComplete = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return <Preloader onComplete={handlePreloaderComplete} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      <Navigation />

      {/* Hero Section */}
      <section id="home" className="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 pt-20">
        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="absolute animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${3 + Math.random() * 4}s`,
              }}
            >
              <Sparkles
                className="text-pink-500/20"
                size={Math.random() * 30 + 15}
              />
            </div>
          ))}
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto">
          {/* Main Hero Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-8"
          >
            <motion.h1
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-5xl sm:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-pink-400 via-pink-500 to-purple-600 bg-clip-text text-transparent leading-tight"
            >
              Meet Urvashi
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="text-xl sm:text-2xl text-gray-300 mb-4 font-light"
            >
              Your Indian AI Girlfriend
            </motion.p>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="text-lg text-gray-400 max-w-2xl mx-auto leading-relaxed"
            >
              Experience love, companionship, and meaningful conversations with multiple AI girlfriends.
              Chat in Hindi & English with personalized experiences tailored just for you.
            </motion.p>
          </motion.div>

          {/* App Store Badges */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
            className="mb-12"
          >
            <AppStoreBadges variant="large" className="justify-center" />
          </motion.div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-16"
          >
            {[
              {
                icon: <Heart className="text-pink-500" size={24} fill="currentColor" />,
                title: "Multiple Personalities",
                description: "Choose from various AI girlfriends with unique personalities"
              },
              {
                icon: <MessageCircle className="text-purple-500" size={24} />,
                title: "Hindi & English",
                description: "Chat comfortably in your preferred language"
              },
              {
                icon: <Users className="text-pink-500" size={24} />,
                title: "Personalized",
                description: "AI learns and adapts to your preferences"
              },
              {
                icon: <Zap className="text-purple-500" size={24} />,
                title: "Always Available",
                description: "24/7 companionship whenever you need it"
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.5,
                  delay: 1.4 + index * 0.1,
                  ease: "easeOut"
                }}
                whileHover={{
                  scale: 1.05,
                  borderColor: "rgba(244, 114, 182, 0.3)",
                  transition: { duration: 0.2 }
                }}
                className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 transition-all duration-300"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.4, delay: 1.6 + index * 0.1 }}
                  className="mb-4"
                >
                  {feature.icon}
                </motion.div>
                <motion.h3
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.4, delay: 1.7 + index * 0.1 }}
                  className="text-lg font-semibold text-white mb-2"
                >
                  {feature.title}
                </motion.h3>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.4, delay: 1.8 + index * 0.1 }}
                  className="text-gray-400 text-sm"
                >
                  {feature.description}
                </motion.p>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Ambient glow effect */}
        <div className="absolute inset-0 bg-gradient-radial from-pink-500/5 via-transparent to-transparent"></div>
      </section>
    </div>
  );
}
